(define-constant token-decimals u6)
(define-constant total-supply u1000000) ;; 1,000,000 tokens max

(define-data-var balances (map principal uint)) ;; token balances
(define-data-var total-rent-collected uint u0)
(define-data-var total-distributed uint u0)

(define-data-var manager principal tx-sender)

(define-map maintenance-log 
  { id: uint }
  { description: (string-ascii 200), timestamp: uint })

(define-data-var log-counter uint u0)

;; Events
(define-event rent-distributed (amount uint))
(define-event maintenance-added (id uint))

;; Token transfer
(define-public (transfer (to principal) (amount uint))
  (let ((sender tx-sender))
    (let ((sender-balance (default-to u0 (map-get? balances sender))))
      (begin
        (asserts! (>= sender-balance amount) (err "Insufficient balance"))
        (map-set balances sender (- sender-balance amount))
        (map-set balances to (+ (default-to u0 (map-get? balances to)) amount))
        (ok true)))))

;; Initial mint - only callable once by the manager
(define-public (mint-tokens (recipient principal) (amount uint))
  (begin
    (asserts! (is-eq tx-sender (var-get manager)) (err "Only manager can mint"))
    (let ((existing (default-to u0 (map-get? balances recipient))))
      (map-set balances recipient (+ existing amount))
      (ok true))))

;; Manager deposits rental income into contract
(define-public (deposit-rent (amount uint))
  (begin
    (asserts! (is-eq tx-sender (var-get manager)) (err "Only manager can deposit rent"))
    (var-set total-rent-collected (+ (var-get total-rent-collected) amount))
    (ok true)))

;; Distribute rent to all investors
(define-public (distribute-rent)
  (begin
    (asserts! (is-eq tx-sender (var-get manager)) (err
